name: UnitTests

on: [push]

jobs:
  unittests:
    runs-on: ubuntu-latest
    strategy:
      max-parallel: 4

    steps:
    - uses: actions/checkout@v4
    - name: Set up Python 3.12
      uses: actions/setup-python@v5
      with:
        python-version: '3.12'
    - name: Add conda to system path
      run: |
        # $CONDA is an environment variable pointing to the root of the miniconda directory
        echo $CONDA/bin >> $GITHUB_PATH
    - name: Install dependencies
      run: |
        conda create --name test-env python=3.12
        conda install pytest
        echo "Starting setup from $PWD"
        pip install -e .
    - name: Test with pytest
      run: |
        pytest tests
