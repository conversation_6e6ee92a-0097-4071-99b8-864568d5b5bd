name: <PERSON><PERSON> (Common Code)

on:
  push:
    branches:
      - master
    paths:
      - 'app/'
      - 'evals/*.py'
      - 'src/'
      - 'tests/'
  pull_request:
    branches:
      - master
      - 'gh/**'
    paths:
      - 'app/'
      - 'evals/*.py'
      - 'src/'
      - 'tests/'

jobs:
  run-linters:
    name: Run linters
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'
      - name: Install Python lint dependencies
        run: |
          pip install -r requirements-test.txt
      - name: Set lint paths
        run: echo "lint_paths=app evals/*.py src tests" >> "$GITHUB_ENV"
      - name: Run isort
        run: |
          python -m isort $lint_paths --check
      - name: Run flake8
        if: always()
        run: |
          python -m flake8 --config .flake8 --show-source --statistics $lint_paths
      - name: Run black
        if: always()
        run: |
          python -m black --check $lint_paths
