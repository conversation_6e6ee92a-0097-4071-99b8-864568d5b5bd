{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# V-JEPA 2 Demo Notebook\n", "\n", "This tutorial provides an example of how to load the V-JEPA 2 model in vanilla PyTorch and HuggingFace, extract a video embedding, and then predict an action class. For more details about the paper and model weights, please see https://github.com/facebookresearch/vjepa2."]}, {"cell_type": "markdown", "metadata": {}, "source": ["First, let's import the necessary libraries and load the necessary functions for this tutorial."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages/timm/models/layers/__init__.py:48: FutureWarning: Importing from timm.models.layers is deprecated, please import via timm.layers\n", "  warnings.warn(f\"Importing from {__name__} is deprecated, please import via timm.layers\", FutureWarning)\n"]}], "source": ["import json\n", "import os\n", "import subprocess\n", "import sys\n", "sys.path.append(\"/home/<USER>/work/research/vjepa2/\")\n", "sys.path.append(\"/home/<USER>/work/research/vjepa2/src/\")\n", "\n", "import numpy as np\n", "import torch\n", "import torch.nn.functional as F\n", "from decord import VideoReader\n", "from transformers import AutoVideoProcessor, AutoModel\n", "\n", "\n", "\n", "import src.datasets.utils.video.transforms as video_transforms\n", "import src.datasets.utils.video.volume_transforms as volume_transforms\n", "from src.models.attentive_pooler import AttentiveClassifier\n", "from src.models.vision_transformer import vit_giant_xformers_rope\n", "\n", "IMAGENET_DEFAULT_MEAN = (0.485, 0.456, 0.406)\n", "IMAGENET_DEFAULT_STD = (0.229, 0.224, 0.225)\n", "\n", "def load_pretrained_vjepa_pt_weights(model, pretrained_weights):\n", "    # Load weights of the VJEPA2 encoder\n", "    # The PyTorch state_dict is already preprocessed to have the right key names\n", "    pretrained_dict = torch.load(pretrained_weights, weights_only=True, map_location=\"cpu\")[\"encoder\"]\n", "    pretrained_dict = {k.replace(\"module.\", \"\"): v for k, v in pretrained_dict.items()}\n", "    pretrained_dict = {k.replace(\"backbone.\", \"\"): v for k, v in pretrained_dict.items()}\n", "    msg = model.load_state_dict(pretrained_dict, strict=False)\n", "    print(\"Pretrained weights found at {} and loaded with msg: {}\".format(pretrained_weights, msg))\n", "\n", "\n", "def load_pretrained_vjepa_classifier_weights(model, pretrained_weights):\n", "    # Load weights of the VJEPA2 classifier\n", "    # The PyTorch state_dict is already preprocessed to have the right key names\n", "    pretrained_dict = torch.load(pretrained_weights, weights_only=True, map_location=\"cpu\")[\"classifiers\"][0]\n", "    pretrained_dict = {k.replace(\"module.\", \"\"): v for k, v in pretrained_dict.items()}\n", "    msg = model.load_state_dict(pretrained_dict, strict=False)\n", "    print(\"Pretrained weights found at {} and loaded with msg: {}\".format(pretrained_weights, msg))\n", "\n", "\n", "def build_pt_video_transform(img_size):\n", "    short_side_size = int(256.0 / 224 * img_size)\n", "    # Eval transform has no random cropping nor flip\n", "    eval_transform = video_transforms.Compose(\n", "        [\n", "            video_transforms.Resize(short_side_size, interpolation=\"bilinear\"),\n", "            video_transforms.CenterCrop(size=(img_size, img_size)),\n", "            volume_transforms.ClipToTensor(),\n", "            video_transforms.Normalize(mean=IMAGENET_DEFAULT_MEAN, std=IMAGENET_DEFAULT_STD),\n", "        ]\n", "    )\n", "    return eval_transform\n", "\n", "\n", "def get_video():\n", "    vr = VideoReader(\"/home/<USER>/work/research/vjepa2/notebooks/driving.mp4\")\n", "    # choosing some frames here, you can define more complex sampling strategy\n", "    frame_idx = np.arange(0, 128, 2)\n", "    video = vr.get_batch(frame_idx).asnumpy()\n", "    return video\n", "\n", "\n", "def forward_vjepa_video(model_hf, model_pt, hf_transform, pt_transform):\n", "    # Run a sample inference with VJEPA\n", "    with torch.inference_mode():\n", "        # Read and pre-process the image\n", "        video = get_video()  # T x H x W x C\n", "        video = torch.from_numpy(video).permute(0, 3, 1, 2)  # T x C x H x W\n", "        x_pt = pt_transform(video).cuda().unsqueeze(0)\n", "        x_hf = hf_transform(video, return_tensors=\"pt\")[\"pixel_values_videos\"].to(\"cuda\")\n", "        # Extract the patch-wise features from the last layer\n", "        out_patch_features_pt = model_pt(x_pt)\n", "        out_patch_features_hf = model_hf.get_vision_features(x_hf)\n", "\n", "    return out_patch_features_hf, out_patch_features_pt\n", "\n", "\n", "def get_vjepa_video_classification_results(classifier, out_patch_features_pt):\n", "    SOMETHING_SOMETHING_V2_CLASSES = json.load(open(\"ssv2_classes.json\", \"r\"))\n", "\n", "    with torch.inference_mode():\n", "        out_classifier = classifier(out_patch_features_pt)\n", "\n", "    print(f\"Classifier output shape: {out_classifier.shape}\")\n", "\n", "    print(\"Top 5 predicted class names:\")\n", "    top5_indices = out_classifier.topk(5).indices[0]\n", "    top5_probs = <PERSON>.softmax(out_classifier.topk(5).values[0]) * 100.0  # convert to percentage\n", "    for idx, prob in zip(top5_indices, top5_probs):\n", "        str_idx = str(idx.item())\n", "        print(f\"{SOMETHING_SOMETHING_V2_CLASSES[str_idx]} ({prob}%)\")\n", "\n", "    return"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Next, let's download a sample video to the local repository. If the video is already downloaded, the code will skip this step. Likewise, let's download a mapping for the action recognition classes used in Something-Something V2, so we can interpret the predicted action class from our model."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["sample_video_path = \"/home/<USER>/work/research/vjepa2/notebooks/driving.mp4\"\n", "# Download the video if not yet downloaded to local path\n", "if not os.path.exists(sample_video_path):\n", "    video_url = \"https://huggingface.co/datasets/nateraw/kinetics-mini/resolve/main/val/bowling/-WH-lxmGJVY_000005_000015.mp4\"\n", "    command = [\"wget\", video_url, \"-O\", sample_video_path]\n", "    subprocess.run(command)\n", "    print(\"Downloading video\")\n", "\n", "# Download SSV2 classes if not already present\n", "ssv2_classes_path = \"ssv2_classes.json\"\n", "if not os.path.exists(ssv2_classes_path):\n", "    command = [\n", "        \"wget\",\n", "        \"https://huggingface.co/datasets/huggingface/label-files/resolve/d79675f2d50a7b1ecf98923d42c30526a51818e2/\"\n", "        \"something-something-v2-id2label.json\",\n", "        \"-O\",\n", "        \"ssv2_classes.json\",\n", "    ]\n", "    subprocess.run(command)\n", "    print(\"Downloading SSV2 classes\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, let's load the models in both vanilla Pytorch as well as through the HuggingFace API. Note that HuggingFace API will automatically load the weights through `from_pretrained()`, so there is no additional download required for HuggingFace.\n", "\n", "To download the PyTorch model weights, use wget and specify your preferred target path. See the README for the model weight URLs.\n", "E.g. \n", "```\n", "wget https://dl.fbaipublicfiles.com/vjepa2/vitg-384.pt -P YOUR_DIR\n", "```\n", "Then update `pt_model_path` with `YOUR_DIR/vitg-384.pt`. Also note that you have the option to use `torch.hub.load`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample_video_path = \"/home/<USER>/work/research/vjepa2/notebooks/driving.mp4\""]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Pretrained weights found at /home/<USER>/work/research/vjepa2/pre_trained_ckpts/vitg-384.pt and loaded with msg: <All keys matched successfully>\n"]}], "source": ["# HuggingFace model repo name\n", "hf_model_name = (\n", "    \"facebook/vjepa2-vitg-fpc64-384\"  # Replace with your favored model, e.g. facebook/vjepa2-vitg-fpc64-384\n", ")\n", "# Path to local PyTorch weights\n", "pt_model_path = \"/home/<USER>/work/research/vjepa2/pre_trained_ckpts/vitg-384.pt\"\n", "\n", "# Initialize the HuggingFace model, load pretrained weights\n", "model_hf = AutoModel.from_pretrained(hf_model_name)\n", "model_hf.cuda().eval()\n", "\n", "# Build HuggingFace preprocessing transform\n", "hf_transform = AutoVideoProcessor.from_pretrained(hf_model_name)\n", "img_size = hf_transform.crop_size[\"height\"]  # E.g. 384, 256, etc.\n", "\n", "# Initialize the PyTorch model, load pretrained weights\n", "model_pt = vit_giant_xformers_rope(img_size=(img_size, img_size), num_frames=64)\n", "model_pt.cuda().eval()\n", "load_pretrained_vjepa_pt_weights(model_pt, pt_model_path)\n", "\n", "### Can also use torch.hub to load the model\n", "# model_pt, _ = torch.hub.load('facebookresearch/vjepa2', 'vjepa2_vit_giant_384')\n", "# model_pt.cuda().eval()\n", "\n", "# Build PyTorch preprocessing transform\n", "pt_video_transform = build_pt_video_transform(img_size=img_size)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now we can run the encoder on the video to get the patch-wise features from the last layer of the encoder. To verify that the HuggingFace and PyTorch models are equivalent, we will compare the values of the features."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["VJEPA2Model(\n", "  (encoder): VJEPA2Encoder(\n", "    (embeddings): VJEPA2Embeddings(\n", "      (patch_embeddings): VJEPA2PatchEmbeddings3D(\n", "        (proj): Conv3d(3, 1408, kernel_size=(2, 16, 16), stride=(2, 16, 16))\n", "      )\n", "    )\n", "    (layer): ModuleList(\n", "      (0-39): 40 x VJEPA2Layer(\n", "        (norm1): LayerNorm((1408,), eps=1e-06, elementwise_affine=True)\n", "        (attention): VJEPA2RopeAttention(\n", "          (query): Linear(in_features=1408, out_features=1408, bias=True)\n", "          (key): Linear(in_features=1408, out_features=1408, bias=True)\n", "          (value): Linear(in_features=1408, out_features=1408, bias=True)\n", "          (proj): Linear(in_features=1408, out_features=1408, bias=True)\n", "          (dropout): Dropout(p=0.0, inplace=False)\n", "        )\n", "        (drop_path): Identity()\n", "        (norm2): LayerNorm((1408,), eps=1e-06, elementwise_affine=True)\n", "        (mlp): VJEPA2MLP(\n", "          (fc1): Linear(in_features=1408, out_features=6144, bias=True)\n", "          (activation): GELUActivation()\n", "          (fc2): Linear(in_features=6144, out_features=1408, bias=True)\n", "        )\n", "      )\n", "    )\n", "    (layernorm): LayerNorm((1408,), eps=1e-06, elementwise_affine=True)\n", "  )\n", "  (predictor): VJEPA2Predictor(\n", "    (embeddings): VJEPA2PredictorEmbeddings(\n", "      (predictor_embeddings): Linear(in_features=1408, out_features=384, bias=True)\n", "    )\n", "    (layer): ModuleList(\n", "      (0-11): 12 x VJEPA2Layer(\n", "        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)\n", "        (attention): VJEPA2RopeAttention(\n", "          (query): Linear(in_features=384, out_features=384, bias=True)\n", "          (key): Linear(in_features=384, out_features=384, bias=True)\n", "          (value): Linear(in_features=384, out_features=384, bias=True)\n", "          (proj): Linear(in_features=384, out_features=384, bias=True)\n", "          (dropout): Dropout(p=0.0, inplace=False)\n", "        )\n", "        (drop_path): Identity()\n", "        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)\n", "        (mlp): VJEPA2MLP(\n", "          (fc1): Linear(in_features=384, out_features=1536, bias=True)\n", "          (activation): GELUActivation()\n", "          (fc2): Linear(in_features=1536, out_features=384, bias=True)\n", "        )\n", "      )\n", "    )\n", "    (layernorm): LayerNorm((384,), eps=1e-06, elementwise_affine=True)\n", "    (proj): Linear(in_features=384, out_features=1408, bias=True)\n", "  )\n", ")"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["model_hf"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: peft in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (0.15.2)\n", "Requirement already satisfied: numpy>=1.17 in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from peft) (2.3.0)\n", "Requirement already satisfied: packaging>=20.0 in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from peft) (25.0)\n", "Requirement already satisfied: psutil in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from peft) (7.0.0)\n", "Requirement already satisfied: pyyaml in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from peft) (6.0.2)\n", "Requirement already satisfied: torch>=1.13.0 in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from peft) (2.7.1)\n", "Requirement already satisfied: transformers in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from peft) (4.53.0.dev0)\n", "Requirement already satisfied: tqdm in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from peft) (4.67.1)\n", "Requirement already satisfied: accelerate>=0.21.0 in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from peft) (1.7.0)\n", "Requirement already satisfied: safetensors in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from peft) (0.5.3)\n", "Requirement already satisfied: huggingface_hub>=0.25.0 in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from peft) (0.33.0)\n", "Requirement already satisfied: filelock in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from huggingface_hub>=0.25.0->peft) (3.18.0)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from huggingface_hub>=0.25.0->peft) (2025.5.1)\n", "Requirement already satisfied: requests in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from huggingface_hub>=0.25.0->peft) (2.32.4)\n", "Requirement already satisfied: typing-extensions>=3.7.4.3 in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from huggingface_hub>=0.25.0->peft) (4.14.0)\n", "Requirement already satisfied: hf-xet<2.0.0,>=1.1.2 in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from huggingface_hub>=0.25.0->peft) (1.1.4)\n", "Requirement already satisfied: setuptools in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from torch>=1.13.0->peft) (78.1.1)\n", "Requirement already satisfied: sympy>=1.13.3 in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from torch>=1.13.0->peft) (1.14.0)\n", "Requirement already satisfied: networkx in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from torch>=1.13.0->peft) (3.5)\n", "Requirement already satisfied: jinja2 in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from torch>=1.13.0->peft) (3.1.6)\n", "Requirement already satisfied: nvidia-cuda-nvrtc-cu12==12.6.77 in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from torch>=1.13.0->peft) (12.6.77)\n", "Requirement already satisfied: nvidia-cuda-runtime-cu12==12.6.77 in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from torch>=1.13.0->peft) (12.6.77)\n", "Requirement already satisfied: nvidia-cuda-cupti-cu12==12.6.80 in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from torch>=1.13.0->peft) (12.6.80)\n", "Requirement already satisfied: nvidia-cudnn-cu12==9.5.1.17 in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from torch>=1.13.0->peft) (9.5.1.17)\n", "Requirement already satisfied: nvidia-cublas-cu12==12.6.4.1 in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from torch>=1.13.0->peft) (12.6.4.1)\n", "Requirement already satisfied: nvidia-cufft-cu12==11.3.0.4 in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from torch>=1.13.0->peft) (11.3.0.4)\n", "Requirement already satisfied: nvidia-curand-cu12==10.3.7.77 in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from torch>=1.13.0->peft) (10.3.7.77)\n", "Requirement already satisfied: nvidia-cusolver-cu12==11.7.1.2 in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from torch>=1.13.0->peft) (11.7.1.2)\n", "Requirement already satisfied: nvidia-cusparse-cu12==12.5.4.2 in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from torch>=1.13.0->peft) (12.5.4.2)\n", "Requirement already satisfied: nvidia-cusparselt-cu12==0.6.3 in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from torch>=1.13.0->peft) (0.6.3)\n", "Requirement already satisfied: nvidia-nccl-cu12==2.26.2 in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from torch>=1.13.0->peft) (2.26.2)\n", "Requirement already satisfied: nvidia-nvtx-cu12==12.6.77 in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from torch>=1.13.0->peft) (12.6.77)\n", "Requirement already satisfied: nvidia-nvjitlink-cu12==12.6.85 in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from torch>=1.13.0->peft) (12.6.85)\n", "Requirement already satisfied: nvidia-cufile-cu12==1.11.1.6 in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from torch>=1.13.0->peft) (1.11.1.6)\n", "Requirement already satisfied: triton==3.3.1 in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from torch>=1.13.0->peft) (3.3.1)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from sympy>=1.13.3->torch>=1.13.0->peft) (1.3.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from jinja2->torch>=1.13.0->peft) (3.0.2)\n", "Requirement already satisfied: charset_normalizer<4,>=2 in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from requests->huggingface_hub>=0.25.0->peft) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from requests->huggingface_hub>=0.25.0->peft) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from requests->huggingface_hub>=0.25.0->peft) (2.5.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from requests->huggingface_hub>=0.25.0->peft) (2025.6.15)\n", "Requirement already satisfied: regex!=2019.12.17 in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from transformers->peft) (2024.11.6)\n", "Requirement already satisfied: tokenizers<0.22,>=0.21 in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from transformers->peft) (0.21.1)\n", "Collecting laspy\n", "  Using cached laspy-2.5.4-py3-none-any.whl.metadata (3.5 kB)\n", "Requirement already satisfied: numpy in /home/<USER>/miniconda3/envs/vjepa2/lib/python3.12/site-packages (from laspy) (2.3.0)\n", "Using cached laspy-2.5.4-py3-none-any.whl (84 kB)\n", "Installing collected packages: laspy\n", "Successfully installed laspy-2.5.4\n", "Collecting <PERSON><PERSON><PERSON>\n", "  Downloading ujson-5.10.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (9.3 kB)\n", "Downloading ujson-5.10.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (53 kB)\n", "Installing collected packages: ujson\n", "Successfully installed ujson-5.10.0\n"]}], "source": ["!pip install peft\n", "!pip install laspy\n", "!pip install ujson\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["vjepa_model = model_hf"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["from peft import LoraConfig, get_peft_model\n", "lora_config = LoraConfig(\n", "    r=16,\n", "    lora_alpha=32,\n", "    target_modules=[\"query\", \"key\", \"value\"],\n", "    lora_dropout=0.1,\n", ")\n", "vjepa_model = get_peft_model(vjepa_model, lora_config)\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["PeftModel(\n", "  (base_model): LoraModel(\n", "    (model): VJEPA2Model(\n", "      (encoder): VJEPA2Encoder(\n", "        (embeddings): VJEPA2Embeddings(\n", "          (patch_embeddings): VJEPA2PatchEmbeddings3D(\n", "            (proj): Conv3d(3, 1408, kernel_size=(2, 16, 16), stride=(2, 16, 16))\n", "          )\n", "        )\n", "        (layer): ModuleList(\n", "          (0-39): 40 x VJEPA2Layer(\n", "            (norm1): LayerNorm((1408,), eps=1e-06, elementwise_affine=True)\n", "            (attention): VJEPA2RopeAttention(\n", "              (query): lora.<PERSON>ar(\n", "                (base_layer): Linear(in_features=1408, out_features=1408, bias=True)\n", "                (lora_dropout): ModuleDict(\n", "                  (default): Dropout(p=0.1, inplace=False)\n", "                )\n", "                (lora_A): ModuleDict(\n", "                  (default): Linear(in_features=1408, out_features=16, bias=False)\n", "                )\n", "                (lora_B): ModuleDict(\n", "                  (default): Linear(in_features=16, out_features=1408, bias=False)\n", "                )\n", "                (lora_embedding_A): ParameterDict()\n", "                (lora_embedding_B): ParameterDict()\n", "                (lora_magnitude_vector): ModuleDict()\n", "              )\n", "              (key): lo<PERSON>.<PERSON>(\n", "                (base_layer): Linear(in_features=1408, out_features=1408, bias=True)\n", "                (lora_dropout): ModuleDict(\n", "                  (default): Dropout(p=0.1, inplace=False)\n", "                )\n", "                (lora_A): ModuleDict(\n", "                  (default): Linear(in_features=1408, out_features=16, bias=False)\n", "                )\n", "                (lora_B): ModuleDict(\n", "                  (default): Linear(in_features=16, out_features=1408, bias=False)\n", "                )\n", "                (lora_embedding_A): ParameterDict()\n", "                (lora_embedding_B): ParameterDict()\n", "                (lora_magnitude_vector): ModuleDict()\n", "              )\n", "              (value): lora.Linear(\n", "                (base_layer): Linear(in_features=1408, out_features=1408, bias=True)\n", "                (lora_dropout): ModuleDict(\n", "                  (default): Dropout(p=0.1, inplace=False)\n", "                )\n", "                (lora_A): ModuleDict(\n", "                  (default): Linear(in_features=1408, out_features=16, bias=False)\n", "                )\n", "                (lora_B): ModuleDict(\n", "                  (default): Linear(in_features=16, out_features=1408, bias=False)\n", "                )\n", "                (lora_embedding_A): ParameterDict()\n", "                (lora_embedding_B): ParameterDict()\n", "                (lora_magnitude_vector): ModuleDict()\n", "              )\n", "              (proj): Linear(in_features=1408, out_features=1408, bias=True)\n", "              (dropout): Dropout(p=0.0, inplace=False)\n", "            )\n", "            (drop_path): Identity()\n", "            (norm2): LayerNorm((1408,), eps=1e-06, elementwise_affine=True)\n", "            (mlp): VJEPA2MLP(\n", "              (fc1): Linear(in_features=1408, out_features=6144, bias=True)\n", "              (activation): GELUActivation()\n", "              (fc2): Linear(in_features=6144, out_features=1408, bias=True)\n", "            )\n", "          )\n", "        )\n", "        (layernorm): LayerNorm((1408,), eps=1e-06, elementwise_affine=True)\n", "      )\n", "      (predictor): VJEPA2Predictor(\n", "        (embeddings): VJEPA2PredictorEmbeddings(\n", "          (predictor_embeddings): Linear(in_features=1408, out_features=384, bias=True)\n", "        )\n", "        (layer): ModuleList(\n", "          (0-11): 12 x VJEPA2Layer(\n", "            (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)\n", "            (attention): VJEPA2RopeAttention(\n", "              (query): lora.<PERSON>ar(\n", "                (base_layer): Linear(in_features=384, out_features=384, bias=True)\n", "                (lora_dropout): ModuleDict(\n", "                  (default): Dropout(p=0.1, inplace=False)\n", "                )\n", "                (lora_A): ModuleDict(\n", "                  (default): Linear(in_features=384, out_features=16, bias=False)\n", "                )\n", "                (lora_B): ModuleDict(\n", "                  (default): Linear(in_features=16, out_features=384, bias=False)\n", "                )\n", "                (lora_embedding_A): ParameterDict()\n", "                (lora_embedding_B): ParameterDict()\n", "                (lora_magnitude_vector): ModuleDict()\n", "              )\n", "              (key): lo<PERSON>.<PERSON>(\n", "                (base_layer): Linear(in_features=384, out_features=384, bias=True)\n", "                (lora_dropout): ModuleDict(\n", "                  (default): Dropout(p=0.1, inplace=False)\n", "                )\n", "                (lora_A): ModuleDict(\n", "                  (default): Linear(in_features=384, out_features=16, bias=False)\n", "                )\n", "                (lora_B): ModuleDict(\n", "                  (default): Linear(in_features=16, out_features=384, bias=False)\n", "                )\n", "                (lora_embedding_A): ParameterDict()\n", "                (lora_embedding_B): ParameterDict()\n", "                (lora_magnitude_vector): ModuleDict()\n", "              )\n", "              (value): lora.Linear(\n", "                (base_layer): Linear(in_features=384, out_features=384, bias=True)\n", "                (lora_dropout): ModuleDict(\n", "                  (default): Dropout(p=0.1, inplace=False)\n", "                )\n", "                (lora_A): ModuleDict(\n", "                  (default): Linear(in_features=384, out_features=16, bias=False)\n", "                )\n", "                (lora_B): ModuleDict(\n", "                  (default): Linear(in_features=16, out_features=384, bias=False)\n", "                )\n", "                (lora_embedding_A): ParameterDict()\n", "                (lora_embedding_B): ParameterDict()\n", "                (lora_magnitude_vector): ModuleDict()\n", "              )\n", "              (proj): Linear(in_features=384, out_features=384, bias=True)\n", "              (dropout): Dropout(p=0.0, inplace=False)\n", "            )\n", "            (drop_path): Identity()\n", "            (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)\n", "            (mlp): VJEPA2MLP(\n", "              (fc1): Linear(in_features=384, out_features=1536, bias=True)\n", "              (activation): GELUActivation()\n", "              (fc2): Linear(in_features=1536, out_features=384, bias=True)\n", "            )\n", "          )\n", "        )\n", "        (layernorm): LayerNorm((384,), eps=1e-06, elementwise_affine=True)\n", "        (proj): Linear(in_features=384, out_features=1408, bias=True)\n", "      )\n", "    )\n", "  )\n", ")"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["vjepa_model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Inference on video to get the patch-wise features\n", "out_patch_features_hf, out_patch_features_pt = forward_vjepa_video(\n", "    model_hf, model_pt, hf_transform, pt_video_transform\n", ")\n", "\n", "print(\n", "    f\"\"\"\n", "    Inference results on video:\n", "    HuggingFace output shape: {out_patch_features_hf.shape}\n", "    PyTorch output shape:     {out_patch_features_pt.shape}\n", "    Absolute difference sum:  {torch.abs(out_patch_features_pt - out_patch_features_hf).sum():.6f}\n", "    Close: {torch.allclose(out_patch_features_pt, out_patch_features_hf, atol=1e-3, rtol=1e-3)}\n", "    \"\"\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Great! Now we know that the features from both models are equivalent. Now let's run a pretrained attentive probe classifier on top of the extracted features, to predict an action class for the video. Let's use the Something-Something V2 probe. Note that the repository also includes attentive probe weights for other evaluations such as EPIC-KITCHENS-100 and Diving48.\n", "\n", "To download the attentive probe weights, use wget and specify your preferred target path. E.g. `wget https://dl.fbaipublicfiles.com/vjepa2/evals/ssv2-vitg-384-64x2x3.pt -P YOUR_DIR`\n", "\n", "Then update `classifier_model_path` with `YOUR_DIR/ssv2-vitg-384-64x2x3.pt`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize the classifier\n", "classifier_model_path = \"/home/<USER>/work/research/vjepa2/pre_trained_ckpts/ssv2-vitg-384-64x2x3.pt\"\n", "classifier = (\n", "    AttentiveClassifier(embed_dim=model_pt.embed_dim, num_heads=16, depth=4, num_classes=174).cuda().eval()\n", ")\n", "load_pretrained_vjepa_classifier_weights(classifier, classifier_model_path)\n", "\n", "# Get classification results\n", "get_vjepa_video_classification_results(classifier, out_patch_features_pt)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The video features a man putting a bowling ball into a tube, so the predicted action of \"Putting [something] into [something]\" makes sense!\n", "\n", "This concludes the tutorial. Please see the README and paper for full details on the capabilities of V-JEPA 2 :)"]}], "metadata": {"fileHeader": "", "fileUid": "f0b70ba6-1c84-47e1-81bd-b7642f9acf50", "isAdHoc": false, "kernelspec": {"display_name": "vjepa2", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 2}