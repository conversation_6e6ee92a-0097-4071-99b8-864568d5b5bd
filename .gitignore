*.pyc
.vscode/
.*.swp

run_vjepa_aws.py
run.py
main_distributed_video.py
main_video.py

app/vjepa/configs/temp_aws
app/main_dev.py
app/main_distributed_dev.py
evals/ava/alphaction/data

run_evals.py
run_evals_v2.py
run_pretrain.py

*.egg-info/
*.ipynb_checkpoints/

traces/
third_party/*

evals/simu_env_planning/local/
evals/simu_env_planning/docker2/
evals/simu_env_planning/docker/
app/vjepa_droid/local/
app/vjepa_droid_v2/local/
app/vjepa_droid_v3/local/
app/vjepa_droid_v4/local/
configs/local