app: vjepa_droid
cpus_per_task: 12
folder: /your_folder/droid/4.8.vitg16-256px-8f
mem_per_gpu: 220G
nodes: 4
tasks_per_node: 8
data:
  batch_size: 8
  camera_views:
  - left_mp4_path
  crop_size: 256
  datasets:
    - /your_file_path/droid_train_paths_cw.csv
  dataset_fpcs:
  - 8
  fps: 4
  num_workers: 12
  patch_size: 16
  pin_mem: true
  stereo_view: false
  tubelet_size: 2
data_aug:
  auto_augment: false
  horizontal_flip: false
  motion_shift: false
  random_resize_aspect_ratio:
  - 0.75
  - 1.35
  random_resize_scale:
  - 1.777
  - 1.777
  reprob: 0.0
loss:
  auto_steps: 2
  loss_exp: 1.0
  normalize_reps: true
  reg_coeff: 0.0
meta:
  dtype: bfloat16
  eval_freq: 100
  resume_checkpoint: null
  load_predictor: false
  pretrain_checkpoint: /your_vjepa2_checkpoints/vitg.pt
  context_encoder_key: target_encoder
  target_encoder_key: target_encoder
  save_every_freq: 25
  seed: 239
  use_sdpa: true
model:
  model_name: vit_giant_xformers
  pred_depth: 24
  pred_embed_dim: 1024
  pred_is_frame_causal: true
  pred_num_heads: 16
  uniform_power: true
  use_activation_checkpointing: true
  use_extrinsics: false
  use_rope: true
optimization:
  anneal: 15
  epochs: 315
  final_lr: 0.0
  final_weight_decay: 0.04
  ipe: 300
  lr: 0.000425
  start_lr: 0.000075
  warmup: 15
  weight_decay: 0.04
